import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>b, Card, Form, Button, Input, DatePicker, Flex, Pagination, Image, Row, Col, message, Empty, Modal, Drawer, Select } from 'antd';
import { RedoOutlined } from '@ant-design/icons';
import FolderOpen from '../../../public/icons/FolderOpen.png'
import hole from '../../../public/icons/hole.png'
import { postRequest, getRequest } from '@/services/api/api';
import { Descriptions } from 'antd';
import EndHoleReport from '../Project/Construction/component/endHoleReport';
import TodoDetail from './components/TodoDetail';
// import type { DescriptionsProps } from 'antd';
import { history } from '@umijs/max';
import mqtt from 'mqtt';

const { RangePicker } = DatePicker;
const { TextArea } = Input;

interface TodoItem {
  id: string;
  title: string;
  submitter: string;
  createdAt: string;
  dynamicFields: string | Record<string, any>;
  status?: number;
  type?: number;
  correlationId?: number;
  // ... 其他字段
}

interface ApiResponse<T> {
  status: number;
  msg?: string;
  data: T;
}

interface TodoListResponse {
  items: TodoItem[];
  total: number;
}

const Todo: React.FC = () => {
  const [form] = Form.useForm();
  const [todoList, setTodoList] = useState<TodoItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [messageApi, contextHolder] = message.useMessage();
  const [loadingIds, setLoadingIds] = useState<string[]>([]);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [currentRejectId, setCurrentRejectId] = useState<string>('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  // 注释掉不再使用的状态
  // const [open, setOpen] = useState(false);
  // const [initialValues, setInitialValues] = useState<Todo | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [number, setNumber] = useState<number>()
  const [silentLoading, setSilentLoading] = useState(false);
  // 新增：TodoDetail组件相关状态
  const [todoDetailVisible, setTodoDetailVisible] = useState(false);
  const [currentTodoId, setCurrentTodoId] = useState<number | undefined>(undefined);

  // MQTT client reference
  const mqttClient = useRef<mqtt.MqttClient | null>(null);

  // const [itemsA, setItems] = useState([]);
  // 流程
  const [status, setStatus] = useState(1);
  // 注释掉不再使用的函数
  // const onClose = () => {
  //   setOpen(false);
  // };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const statusMap = {
    0: { title: '审批中', color: '#D89614' },
    1: { title: '已同意', color: '#52c41a' },
    2: { title: '已拒绝', color: '#fe5052' }
  };
  const handleChange = (value: string) => {
    // console.log(`selected ${value}`);
    const newStatus = Number(value);
    setStatus(newStatus);
    // 立即触发筛选
    const values = form.getFieldsValue();
    const { dateRange, ...restValues } = values;
    fetchTodoList({
      page: 1, // 重置到第一页
      perPage: 10,
      ...restValues,
      status: newStatus, // 使用新的状态值
      ...(dateRange ? {
        timeStart: dateRange[0]?.format('YYYY-MM-DD HH:mm:ss'),
        timeEnd: dateRange[1]?.format('YYYY-MM-DD HH:mm:ss')
      } : {})
    });
    setCurrentPage(1); // 重置页码
  };
  // 获取待办事项列表
  const fetchTodoList = async (params: any, silent = false) => {
    if (!silent) {
      setLoading(true);
    } else {
      setSilentLoading(true);
    }

    try {
      const result = await postRequest<ApiResponse<TodoListResponse>>('todo/get_ls', {
        ...params,
        type: 0,
      });
      if (result.status === 0) {
        // 处理 dynamicFields 字段
        const processedItems = result.data.items.map(item => ({
          ...item,
          dynamicFields: typeof item.dynamicFields === 'string'
            ? JSON.parse(item.dynamicFields || '{}')
            : item.dynamicFields
        }));
        setTodoList(processedItems);
        setTotal(result.data.total);
      } else {
        if (!silent) {
          messageApi.error(result.msg || '获取数据失败');
        }
      }
    } catch (error) {
      if (!silent) {
        messageApi.error('请求失败');
      }
    } finally {
      if (!silent) {
        setLoading(false);
      } else {
        setSilentLoading(false);
      }
    }
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    const values = form.getFieldsValue();
    fetchTodoList({ page, perPage: 10, status: values.status || status });
  };

  // 处理表单提交
  const handleSubmit = async () => {
    const values = form.getFieldsValue();
    const { dateRange, ...restValues } = values;
    const params = {
      page: currentPage,
      perPage: 10,
      ...restValues,
      ...(dateRange ? {
        timeStart: dateRange[0]?.format('YYYY-MM-DD HH:mm:ss'),
        timeEnd: dateRange[1]?.format('YYYY-MM-DD HH:mm:ss')
      } : {})
    };
    console.log('提交参数：111111', params);
    await fetchTodoList(params);
  };

  // 处理表单重置
  const handleReset = () => {
    form.resetFields();
    setStatus(1); // 重置状态为待处理
    fetchTodoList({ page: 1, perPage: 10, status: 1 });
  };

  // 修改：处理同意操作 - 弹出抽屉
  const handleAgree = (id: string) => {
    // 弹出待办详情抽屉
    setCurrentTodoId(parseInt(id));
    setTodoDetailVisible(true);
  };

  // 新增：审批成功后刷新列表的回调函数
  const handleApprovalSuccess = () => {
    // 刷新当前页面的待办列表
    const values = form.getFieldsValue();
    const { dateRange, ...restValues } = values;
    fetchTodoList({
      page: currentPage,
      perPage: 10,
      ...restValues,
      status: status,
      ...(dateRange ? {
        timeStart: dateRange[0]?.format('YYYY-MM-DD HH:mm:ss'),
        timeEnd: dateRange[1]?.format('YYYY-MM-DD HH:mm:ss')
      } : {})
    });
  };

  // 修改：处理拒绝操作
  const handleReject = async (id: string) => {
    setCurrentRejectId(id);
    setRejectReason('');
    setRejectModalVisible(true);
  };

  // 新增：处理拒绝确认
  const handleRejectConfirm = async () => {
    if (!rejectReason.trim()) {
      messageApi.error('请输入拒绝理由');
      return;
    }

    setLoadingIds(prev => [...prev, currentRejectId]);
    try {
      const result = await postRequest<ApiResponse<null>>('todo/post_modify', {
        id: currentRejectId,
        action: 2,
        refuse: rejectReason,
      });
      if (result.status === 0) {
        messageApi.success('操作成功');
        setRejectModalVisible(false);
        // 刷新列表
        const values = form.getFieldsValue();
        fetchTodoList({
          page: currentPage,
          perPage: 10,
          status: values.status || status,
          type: 0,
        });
      } else {
        messageApi.error(result.msg || '操作失败');
      }
    } catch (error) {
      messageApi.error('请求失败');
    } finally {
      setLoadingIds(prev => prev.filter(item => item !== currentRejectId));
    }
  };

  // 新增：处理拒绝取消
  const handleRejectCancel = () => {
    setRejectModalVisible(false);
    setRejectReason('');
    setCurrentRejectId('');
  };

  // 修改获取列表函数
  const handleRefresh = async () => {
    setIsRefreshing(true);
    const values = form.getFieldsValue();
    const { dateRange, ...restValues } = values;
    await fetchTodoList({
      page: currentPage,
      perPage: 10,
      ...restValues,
      ...(dateRange ? {
        timeStart: dateRange[0]?.format('YYYY-MM-DD HH:mm:ss'),
        timeEnd: dateRange[1]?.format('YYYY-MM-DD HH:mm:ss')
      } : {})
    });
    setTimeout(() => {
      setIsRefreshing(false);
    }, 500);
  };

  const getImageSource = (type?: number) => type === 1 ? FolderOpen : hole;
  const PageListItem = ({ id, title, submitter, createdAt, dynamicFields, status, type, correlationId }: TodoItem) => {
    const isLoading = loadingIds.includes(id);
    const handleTitleClick = () => {
      if (type == 1) {
        // 使用新的TodoDetail组件
        setCurrentTodoId(parseInt(id));
        setTodoDetailVisible(true);
      } else {
        setIsModalOpen(true);
        setNumber(correlationId)
        // alert('1111')
      }
      // history.push(`/todo/detail?id=${id}`);
    };
    // 注释掉不再使用的getInfo函数
    /*
    const getInfo = async (id: string) => {
      setLoading(true);
      try {
        const result = await getRequest<ApiResponse<Todo>>('todo/get_info', { id });
        if (result.status === 0) {
          const data = {
            ...result.data,
            dynamicFields: typeof result.data.dynamicFields === 'string'
              ? JSON.parse(result.data.dynamicFields || '{}')
              : result.data.dynamicFields
          };

          setInitialValues(data);
        } else {
          // setMsg(result.msg || '获取数据失败');
          messageApi.error(result.msg || '获取数据失败');
        }
      } catch (error) {
        // setMsg('请求失败');
        messageApi.error('请求失败');
      } finally {
        setLoading(false);
      }
    };
    */

    return (
      <div>
        <Flex justify="space-between" style={{ padding: '24px 0', borderBottom: '1px solid #434343' }}>
          <Flex style={{ flex: 1, overflow: 'hidden' }}>
            <Image
              width={40}
              src={getImageSource(type)}
              preview={false}
            />
            <div className='list-content' style={{ marginLeft: '16px', flex: 1, minWidth: 0 }}>
              <div
                className='list-content-title'
                style={{
                  fontSize: '14px',
                  color: '#1890ff',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  cursor: 'pointer'
                }}
                onClick={handleTitleClick}
              >
                {title}
              </div>
              <div style={{ marginTop: '16px', color: '#adadad' }}>
                <Row gutter={[8, 8]}>
                  <Col span={6}>提交人：{submitter}</Col>
                  <Col span={6}>创建时间：{createdAt}</Col>
                  {dynamicFields && typeof dynamicFields === 'object' && Object.entries(dynamicFields).map(([key, value]) => (
                    <Col span={6} key={key}>{value?.title || key}：{value?.value ?? value}</Col>
                  ))}
                </Row>
              </div>
            </div>
          </Flex>
          <div className='list-button' style={{ width: '200px', flexShrink: 0, textAlign: 'right' }}>
            {status !== 1 && status !== 2 && type !== 2 && (
              <>
                <Button
                  onClick={() => handleReject(id)}
                  loading={isLoading}
                  disabled={isLoading}
                >
                  拒绝
                </Button>
                <Button
                  type="primary"
                  style={{ marginLeft: '8px' }}
                  onClick={() => handleAgree(id)}
                  loading={isLoading}
                  disabled={isLoading}
                >
                  同意
                </Button>
              </>
            )}
            {status !== 1 && status !== 2 && type === 2 && (
              <>
                <Button
                  type="primary"
                  style={{ marginLeft: '8px' }}
                  onClick={() => handleAgree(id)}
                  loading={isLoading}
                  disabled={isLoading}
                >
                  确认查收
                </Button>
              </>
            )}
          </div>

        </Flex>
      </div>


    )
  };

  // 初始化获取数据
  useEffect(() => {
    // 初始加载数据
    fetchTodoList({ page: 1, perPage: 10, status: 1 });

    // 设置MQTT客户端
    const mqttUrl = `ws://${(window as any).VITE_APP_MQTT_IP || 'wss.tiefulaigroup.com'}:${(window as any).VITE_APP_MQTT_PORT || '31885'}`;
    console.log('mqttUrl:', mqttUrl);

    const mqttUsername = `${(window as any).VITE_APP_MQTT_USERNAME || 'admin'}`;
    const mqttPassword = `${(window as any).VITE_APP_MQTT_PASSWORD || 'password'}`;

    // 创建MQTT客户端
    mqttClient.current = mqtt.connect(mqttUrl, {
      username: mqttUsername,
      password: mqttPassword,
      clientId: `mqtt_${Math.random().toString(16).slice(2, 10)}`,
      clean: true,
      // reconnectPeriod: 5000,
      keepalive: 60, // 保持连接的心跳间隔，确保连接不会因为不活动而断开
      protocol: 'ws',  // 明确指定使用 WebSocket Secure
      protocolVersion: 4,  // MQTT v3.1.1
      protocolId: 'MQTT'
    });

    // 连接成功回调
    mqttClient.current.on('connect', () => {
      console.log('MQTT连接成功');
      // 订阅主题
      mqttClient.current?.subscribe('/sys/todo/update', (err) => {
        if (err) {
          console.error('MQTT subscription error:', err);
        }
      });
    });

    // 消息接收回调
    mqttClient.current.on('message', (topic, message) => {
      try {
        // 解析MQTT消息
        const mqttData = JSON.parse(message.toString());
        console.log('mqttData', mqttData);

        // 获取本地存储的账号
        const localAccount = sessionStorage.getItem('account');

        // 检查MQTT数据中是否包含本地账号
        if (localAccount && mqttData.includes(localAccount)) {
          // 如果匹配，刷新待办列表
          const values = form.getFieldsValue();
          const { dateRange, keyWord, status = 1 } = values || {};

          fetchTodoList({
            page: currentPage,
            perPage: 10,
            status: status || 1,
            keyWord: keyWord || '',
            ...(dateRange ? {
              timeStart: dateRange[0]?.format('YYYY-MM-DD HH:mm:ss'),
              timeEnd: dateRange[1]?.format('YYYY-MM-DD HH:mm:ss')
            } : {})
          }, true);
        }
      } catch (error) {
        console.error('MQTT message parsing error:', error);
      }
    });

    // 错误处理
    mqttClient.current.on('error', (err) => {
      console.error('MQTT error:', err);
    });

    // 组件卸载时断开MQTT连接
    return () => {
      if (mqttClient.current) {
        mqttClient.current.end();
      }
    };
  }, [currentPage]);

  // 渲染列表内容
  const renderListContent = () => {
    if (loading && !silentLoading) {
      return null;
    }

    if (!todoList.length) {
      return (
        <Flex justify="center" align="center" style={{ padding: '32px 0' }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <span style={{ color: '#8c8c8c' }}>暂无待办通知事项</span>
            }
          />
        </Flex>
      );
    }

    return (
      <Flex vertical>
        {todoList.map(item => (
          <PageListItem {...item} key={item.id} />
        ))}
      </Flex>
    );
  };
  return (
    <>
      {contextHolder}
      <Breadcrumb
        items={[
          { title: '首页' },
          { title: '待办' },
        ]}
      />
      <Card style={{ marginTop: '24px' }} bordered={false}>
        <Form

          layout="inline"
          form={form}
          initialValues={{ status: 1 }}
          onFinish={handleSubmit}
        >
          <Flex justify="space-between" style={{ width: '100%' }}>
            <Flex>
              <Form.Item name="keyWord" style={{ marginRight: '16px' }}>
                <Input placeholder="请输入待办事项" />
              </Form.Item>
              <Form.Item name="status" style={{ marginRight: '16px' }}>
                <Select
                  style={{ width: 120 }}
                  onChange={handleChange}
                  options={[
                    { value: 1, label: '待处理' },
                    { value: 2, label: '已处理' },
                  ]}
                />
              </Form.Item>
              <Form.Item name="dateRange">
                <RangePicker placeholder={['开始时间', '结束时间']} />
              </Form.Item>
            </Flex>
            <Form.Item>
              <Button onClick={handleReset}>重置</Button>
              <Button type="primary" htmlType="submit" style={{ marginLeft: '8px' }}>提交</Button>
            </Form.Item>
          </Flex>
        </Form>
      </Card>
      <Card bordered={false} style={{ marginTop: '16px' }}>
        <Flex justify="space-between" align="center">
          <div>待办事项</div>
          <Button
            type="text"


            onClick={handleRefresh}
          />
          {/* icon={
              <RedoOutlined
                style={{
                  fontSize: '18px',
                  transition: 'all 0.3s',
                  transform: 'rotate(-90deg)'
                }}
              />
            } */}
        </Flex>
        {renderListContent()}
        {todoList.length > 0 && (
          <Flex justify="flex-end" style={{ marginTop: '24px' }}>
            <Pagination
              total={total}
              current={currentPage}
              onChange={handlePageChange}
              showTotal={(total) => `总共${total}条`}
            />
          </Flex>
        )}
      </Card>

      {/* 新增：拒绝理由弹窗 */}
      <Modal
        title="拒绝原因"
        open={rejectModalVisible}
        onOk={handleRejectConfirm}
        onCancel={handleRejectCancel}
        confirmLoading={loadingIds.includes(currentRejectId)}
      >
        <Form layout="vertical">
          <Form.Item
            label="请输入拒绝理由"
            required
            rules={[{ required: true, message: '请输入拒绝理由' }]}
          >
            <TextArea
              rows={4}
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="请输入拒绝理由"
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>
      <Modal title="终孔报告单" open={isModalOpen} onCancel={handleCancel} width='70%' footer={[]}>
        <EndHoleReport number={number}></EndHoleReport>
      </Modal>
      {/* 使用新的TodoDetail组件替换原有的简单Drawer */}
      <TodoDetail
        visible={todoDetailVisible}
        onClose={() => setTodoDetailVisible(false)}
        todoId={currentTodoId}
        onApprovalSuccess={handleApprovalSuccess}
      />
    </>
  )
}

export default Todo;