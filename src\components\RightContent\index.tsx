import { QuestionCircleOutlined, BellOutlined } from '@ant-design/icons';
import { SelectLang as UmiSelectLang } from '@umijs/max';
import { Dropdown, Badge } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import NotificationPanel from './NotificationPanel';
import { getRequest } from '@/services/api/api';
import mqtt from 'mqtt';
import Call from '@/pages/Call';
export type SiderTheme = 'light' | 'dark';

export const SelectLang = () => {
  return (
    <UmiSelectLang
      style={{
        padding: 4,
      }}
    />
  );
};

export const Question = () => {
  const [notificationCount, setNotificationCount] = React.useState(0);
  const [dropdownOpen, setDropdownOpen] = React.useState(false);
  const [imInitialized, setImInitialized] = useState(false);
  const callComponentRef = useRef<any>(null);
  const mqttClient = useRef<any>(null);

  // MQTT 连接设置
  useEffect(() => {
    const token = sessionStorage.getItem('sign');
    const account = sessionStorage.getItem('account');

    if (token && account) {
      // 延迟初始化以确保组件已经完全挂载
      setTimeout(async () => {
        if (callComponentRef.current) {
          try {
            await callComponentRef.current.initIMWithDynamicToken();
            setImInitialized(true);
            console.log('IM 初始化成功');
          } catch (error) {
            console.error('IM 初始化失败:', error);
            // message.error('IM 初始化失败，请刷新页面重试');
          }
        }
      }, 500); // 增加延迟时间确保组件完全准备就绪
    }

    handleEdit()
    const mqttUrl = `ws://${VITE_APP_MQTT_IP}:${VITE_APP_MQTT_PORT}`;

    // 创建MQTT客户端
    mqttClient.current = mqtt.connect(mqttUrl, {
      username: `${VITE_APP_MQTT_USERNAME}`,
      password: `${VITE_APP_MQTT_PASSWORD}`,
      clientId: `mqtt_${Math.random().toString(16).slice(2, 10)}`,
      clean: true,
      reconnectPeriod: 5000,
      protocol: 'ws',
      protocolVersion: 4,
      protocolId: 'MQTT'
    });

    // 连接成功回调
    mqttClient.current.on('connect', () => {
      console.log('MQTT连接成功');
      // 订阅通知相关主题
      mqttClient.current?.subscribe('/sys/todo/update', (err: any) => {
        if (err) {
          console.error('MQTT subscription error:', err);
        }
      });
    });

    // 消息接收回调
    mqttClient.current.on('message', (topic: string, message: Buffer) => {
      try {
        const mqttData = JSON.parse(message.toString());
        console.log('收到MQTT消息:', mqttData);
        // 获取本地存储的账号
        const localAccount = sessionStorage.getItem('account');
        if (localAccount && mqttData.includes(localAccount)) {
          handleEdit();
        }
        // 收到消息后重新获取通知数量
      } catch (error) {
        console.error('MQTT消息解析错误:', error);
      }
    });

    // 组件卸载时清理MQTT连接
    return () => {
      if (mqttClient.current) {
        mqttClient.current.end();
      }
    };
  }, []);

  // /drill/get_tode_num
  // 通知数量
  const handleEdit = async () => {
    try {
      const result = await getRequest('drill/get_tode_num');
      const { status, msg, data } = result as any;
      if (status === 0) {
        const dataSource = data.todoNum + data.messageNum
        setNotificationCount(dataSource);
      } else {
        // message.error(msg)
      }
    } catch (error) {
    }
  };

  return (
    <>
      <Dropdown
        dropdownRender={() => <NotificationPanel isOpen={dropdownOpen} />}
        placement="bottomRight"
        arrow={{ pointAtCenter: true }}
        trigger={['hover']}
        onOpenChange={(open) => {
          setDropdownOpen(open);
        }}
      >
        <div
          style={{
            display: 'flex',
            height: 26,
            alignItems: 'center',
            cursor: 'pointer',
          }}
        >
          <BellOutlined style={{ fontSize: 16 }} /> <Badge count={notificationCount} style={{ marginLeft: 2 }} />
        </div>
      </Dropdown>
      {/* 添加 Call 组件 */}
      <Call ref={callComponentRef} />
    </>
  );
};

export const Notification = () => {
  // 添加状态跟踪下拉菜单是否展开
  const [dropdownOpen, setDropdownOpen] = React.useState(false);

  return (
    <Dropdown
      dropdownRender={() => <NotificationPanel isOpen={dropdownOpen} />}
      placement="bottomRight"
      arrow={{ pointAtCenter: true }}
      trigger={['click']}
      onOpenChange={(open) => {
        setDropdownOpen(open);
      }}
    >
      <span style={{ cursor: 'pointer' }}>
        <Badge count={10} size="small">
          <BellOutlined style={{ fontSize: 16 }} />
        </Badge>
      </span>
    </Dropdown>
  );
};
